using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Windows.Media;
using ATAS.Indicators;
using ATAS.Strategies.Chart;

// Enum für Linien-Styles
public enum LineStyle
{
    Solid = 0,
    Dots = 1,
    Squares = 2
}

// Klasse für eine einzelne Wick-Linie
public class WickLine
{
    public decimal Price { get; set; }
    public int StartBar { get; set; }
    public int EndBar { get; set; } // Bar wo die Linie aufhört zu wachsen
    public bool IsActive { get; set; } // Ob die Linie noch wächst
    public bool IsUpperWick { get; set; }
    public ValueDataSeries DataSeries { get; set; }
}

public class WickLinesStrategy : ChartStrategy
{
    #region Private fields

    private List<WickLine> _wickLines;
    private int _lastBar;
    private int _lineCounter = 0; // Zähler für eindeutige Linien-Namen
    private string _logFilePath;

    #endregion

    #region Public properties

    [Display(
        Name = "Minimum Wick Size (Ticks)", 
        Order = 10)]
    [Parameter]
    public int MinimumWickSizeTicks
    {
        get { return _minimumWickSizeTicks; }
        set
        {
            _minimumWickSizeTicks = Math.Max(1, value);
            RecalculateValues();
        }
    }
    private int _minimumWickSizeTicks = 5;

    [Display(
        Name = "Upper Wick Line Color",
        Order = 20)]
    [Parameter]
    public Color UpperWickLineColor { get; set; } = Colors.Red;

    [Display(
        Name = "Lower Wick Line Color",
        Order = 30)]
    [Parameter]
    public Color LowerWickLineColor { get; set; } = Colors.Blue;

    [Display(
        Name = "Line Width",
        Order = 40)]
    [Parameter]
    public int LineWidth { get; set; } = 2;

    [Display(
        Name = "Line Opacity (%)",
        Order = 45)]
    [Parameter]
    public int LineOpacity { get; set; } = 100;

    [Display(
        Name = "Line Style",
        Order = 46)]
    [Parameter]
    public LineStyle LineStyleType { get; set; } = LineStyle.Solid;

    [Display(
        Name = "Show Upper Wick Lines",
        Order = 50)]
    [Parameter]
    public bool ShowUpperWickLines { get; set; } = true;

    [Display(
        Name = "Show Lower Wick Lines",
        Order = 60)]
    [Parameter]
    public bool ShowLowerWickLines { get; set; } = true;

    #endregion

    #region ctor

    public WickLinesStrategy()
    {
        // Verstecke die Standard-DataSeries
        DataSeries[0].IsHidden = true;

        // Initialisiere die Liste für Wick-Linien
        _wickLines = new List<WickLine>();

        // Initialisiere Log-Datei
        _logFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "WickLinesStrategy_Debug.log");

        MinimumWickSizeTicks = 5;
        ShowUpperWickLines = true;
        ShowLowerWickLines = true;
        UpperWickLineColor = Colors.Red;
        LowerWickLineColor = Colors.Blue;
        LineWidth = 2;
        LineOpacity = 100;
        LineStyleType = LineStyle.Solid;
    }

    private void LogDebug(string message)
    {
        try
        {
            var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {message}";
            File.AppendAllText(_logFilePath, logMessage + Environment.NewLine);
        }
        catch
        {
            // Ignore logging errors
        }
    }

    #endregion

    #region Overrides of BaseIndicator

    protected override void OnCalculate(int bar, decimal value)
    {
        var candle = GetCandle(bar);
        if (candle == null)
            return;

        // Aktualisiere bestehende Linien
        UpdateWickLines(bar, candle);

        // Nur bei neuen Kerzen loggen und Linien erstellen
        if (bar > _lastBar)
        {
            _lastBar = bar;
            LogDebug($"=== NEUE KERZE === bar={bar}, CurrentBar={CurrentBar}");
        }

        // Prüfe nur wirklich geschlossene Kerzen (mindestens 2 Bars zurück)
        if (bar < CurrentBar - 1)
        {
            // Prüfe ob bereits Linien für diese Kerze existieren (verhindert Duplikate)
            bool hasUpperLine = _wickLines.Any(l => l.StartBar == bar && l.IsUpperWick);
            bool hasLowerLine = _wickLines.Any(l => l.StartBar == bar && !l.IsUpperWick);

            // Nur verarbeiten wenn noch keine Linien für diese Kerze existieren
            if (!hasUpperLine || !hasLowerLine)
            {
                LogDebug($"=== GESCHLOSSENE KERZE === bar={bar}, CurrentBar={CurrentBar}");
                LogDebug($"OHLC: Open={candle.Open:F2}, High={candle.High:F2}, Low={candle.Low:F2}, Close={candle.Close:F2}");

                // Einfache Logik: Erstelle Linien für jede Kerze, aber verhindere Duplikate
                var tickSize = InstrumentInfo?.TickSize ?? 0.01m;
                var minimumWickSizePrice = MinimumWickSizeTicks * tickSize;

                // Berechne Wick-Größen
                var upperWickSize = CalculateUpperWickSize(candle);
                var lowerWickSize = CalculateLowerWickSize(candle);

                var bodyTop = Math.Max(candle.Open, candle.Close);
                var bodyBottom = Math.Min(candle.Open, candle.Close);

                LogDebug($"Body: Top={bodyTop:F2}, Bottom={bodyBottom:F2}");
                LogDebug($"Wick sizes: upper={upperWickSize:F4}, lower={lowerWickSize:F4}, minRequired={minimumWickSizePrice:F4}");
                LogDebug($"Existing lines: hasUpper={hasUpperLine}, hasLower={hasLowerLine}");

                // Erstelle obere Linie falls noch nicht vorhanden und Bedingungen erfüllt
                if (ShowUpperWickLines && !hasUpperLine && upperWickSize >= minimumWickSizePrice)
                {
                    LogDebug($"Creating UPPER wick line at {candle.High:F2} for bar {bar}");
                    CreateNewWickLine(candle.High, bar, true);
                }

                // Erstelle untere Linie falls noch nicht vorhanden und Bedingungen erfüllt
                if (ShowLowerWickLines && !hasLowerLine && lowerWickSize >= minimumWickSizePrice)
                {
                    LogDebug($"Creating LOWER wick line at {candle.Low:F2} for bar {bar}");
                    CreateNewWickLine(candle.Low, bar, false);
                }

                LogDebug($"=== ENDE GESCHLOSSENE KERZE === Total lines: {_wickLines.Count}");
            }
        }
    }



    #endregion

    #region Private methods

    private decimal CalculateUpperWickSize(IndicatorCandle candle)
    {
        // Oberer Wick = High - Max(Open, Close)
        var bodyTop = Math.Max(candle.Open, candle.Close);
        return candle.High - bodyTop;
    }

    private decimal CalculateLowerWickSize(IndicatorCandle candle)
    {
        // Unterer Wick = Min(Open, Close) - Low
        var bodyBottom = Math.Min(candle.Open, candle.Close);
        return bodyBottom - candle.Low;
    }

    private void CreateNewWickLine(decimal price, int startBar, bool isUpperWick)
    {
        _lineCounter++;
        var lineName = $"WickLine_{_lineCounter}_{(isUpperWick ? "Upper" : "Lower")}";



        // Berechne die Farbe mit Opacity
        var baseColor = isUpperWick ? UpperWickLineColor : LowerWickLineColor;

        var colorWithOpacity = Color.FromArgb(
            (byte)(255 * LineOpacity / 100),
            baseColor.R,
            baseColor.G,
            baseColor.B);

        // Bestimme den VisualMode basierend auf dem LineStyle
        VisualMode visualMode;
        switch (LineStyleType)
        {
            case LineStyle.Solid:
                visualMode = VisualMode.Square;  // Korrigiert: Square für Solid
                break;
            case LineStyle.Dots:
                visualMode = VisualMode.Dots;
                break;
            case LineStyle.Squares:
                visualMode = VisualMode.Line;    // Korrigiert: Line für Squares
                break;
            default:
                visualMode = VisualMode.Square;  // Standard: Square für Solid
                break;
        }

        var dataSeries = new ValueDataSeries(lineName)
        {
            VisualType = visualMode,
            Color = colorWithOpacity,
            Width = LineWidth,
            ShowZeroValue = false,
            IsHidden = true // Verstecke die DataSeries in der UI
        };

        DataSeries.Add(dataSeries);

        var wickLine = new WickLine
        {
            Price = price,
            StartBar = startBar,
            EndBar = -1, // Noch nicht beendet
            IsActive = true,
            IsUpperWick = isUpperWick,
            DataSeries = dataSeries
        };

        _wickLines.Add(wickLine);



        // Setze den ersten Punkt der Linie
        dataSeries[startBar] = price;
    }

    private void UpdateWickLines(int currentBar, IndicatorCandle currentCandle)
    {
        foreach (var line in _wickLines)
        {
            if (!line.IsActive)
                continue;

            // Prüfe ob die Linie vom Preis geschnitten wurde
            bool lineWasCrossed = false;

            if (line.IsUpperWick)
            {
                // Obere Linie wird geschnitten wenn der Preis über sie steigt
                lineWasCrossed = currentCandle.High > line.Price;
            }
            else
            {
                // Untere Linie wird geschnitten wenn der Preis unter sie fällt
                lineWasCrossed = currentCandle.Low < line.Price;
            }

            if (lineWasCrossed)
            {
                // Linie aufhören lassen zu wachsen
                line.IsActive = false;
                line.EndBar = currentBar - 1; // Letzter Bar vor dem Schnitt
            }
            else
            {
                // Linie weiter zeichnen
                line.DataSeries[currentBar] = line.Price;
            }
        }
    }

    #endregion
}
