public class WickLinesStrategyBasic : ATAS.Strategies.Chart.ChartStrategy
{
    #region Private fields

    private ATAS.Indicators.ValueDataSeries _upperWickLines;
    private ATAS.Indicators.ValueDataSeries _lowerWickLines;
    private int _lastBar;
    private int _minimumWickSizeTicks = 5;

    #endregion

    #region Public properties

    public int MinimumWickSizeTicks
    {
        get { return _minimumWickSizeTicks; }
        set
        {
            _minimumWickSizeTicks = value > 0 ? value : 1;
            RecalculateValues();
        }
    }

    #endregion

    #region ctor

    public WickLinesStrategyBasic()
    {
        // Verstecke die Standard-DataSeries
        DataSeries[0].IsHidden = true;

        // Erstelle DataSeries für obere Wick-Linien
        _upperWickLines = new ATAS.Indicators.ValueDataSeries("Upper Wick Lines");
        _upperWickLines.VisualType = ATAS.Indicators.VisualMode.Line;
        _upperWickLines.Color = System.Windows.Media.Colors.Red;
        _upperWickLines.Width = 2;
        DataSeries.Add(_upperWickLines);

        // Erstelle DataSeries für untere Wick-Linien
        _lowerWickLines = new ATAS.Indicators.ValueDataSeries("Lower Wick Lines");
        _lowerWickLines.VisualType = ATAS.Indicators.VisualMode.Line;
        _lowerWickLines.Color = System.Windows.Media.Colors.Blue;
        _lowerWickLines.Width = 2;
        DataSeries.Add(_lowerWickLines);

        MinimumWickSizeTicks = 5;
    }

    #endregion

    #region Overrides of BaseIndicator

    protected override void OnCalculate(int bar, decimal value)
    {
        if (bar < 0 || bar >= CurrentBar)
            return;

        var candle = GetCandle(bar);
        if (candle == null)
            return;

        var tickSize = InstrumentInfo != null ? InstrumentInfo.TickSize : 0.01m;
        var minimumWickSizePrice = MinimumWickSizeTicks * tickSize;

        // Berechne Wick-Größen
        var upperWickSize = CalculateUpperWickSize(candle);
        var lowerWickSize = CalculateLowerWickSize(candle);

        // Setze Werte für obere Wick-Linien
        if (upperWickSize >= minimumWickSizePrice)
        {
            _upperWickLines[bar] = candle.High;
        }
        else
        {
            _upperWickLines[bar] = 0;
        }

        // Setze Werte für untere Wick-Linien
        if (lowerWickSize >= minimumWickSizePrice)
        {
            _lowerWickLines[bar] = candle.Low;
        }
        else
        {
            _lowerWickLines[bar] = 0;
        }

        _lastBar = bar;
    }

    #endregion

    #region Private methods

    private decimal CalculateUpperWickSize(ATAS.Indicators.IndicatorCandle candle)
    {
        // Oberer Wick = High - Max(Open, Close)
        var bodyTop = candle.Open > candle.Close ? candle.Open : candle.Close;
        return candle.High - bodyTop;
    }

    private decimal CalculateLowerWickSize(ATAS.Indicators.IndicatorCandle candle)
    {
        // Unterer Wick = Min(Open, Close) - Low
        var bodyBottom = candle.Open < candle.Close ? candle.Open : candle.Close;
        return bodyBottom - candle.Low;
    }

    #endregion
}
