using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Windows.Media;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using OFT.Attributes;
using ATAS.DataFeedsCore;

/// <summary>
/// POC-Delta Trading Strategy für ATAS
///
/// Long Entry Bedingungen:
/// - Abgeschlossene Kerze ist negativ (Close < Open)
/// - POC liegt innerhalb des Kerzen-Bodies
/// - Delta des POCs ist negativ
/// - Preis läuft 3 Ticks nach unten -> Buy Stop Order am Close der Signalkerze
/// - Order wird gelöscht wenn Trade-Kerze schließt ohne Ausführung
///
/// Short Entry Bedingungen:
/// - Abgeschlossene Kerze ist positiv (Close > Open)
/// - POC liegt innerhalb des Kerzen-Bodies
/// - Delta des POCs ist positiv
/// - Preis läuft 3 Ticks nach oben -> Sell Stop Order am Close der Signalkerze
/// - Order wird gelöscht wenn Trade-Kerze schließt ohne Ausführung
/// </summary>
public class EchelonStrategy : ChartStrategy
{
    #region Private fields

    private int _lastBar = -1;
    private int _signalBar = -1;
    private decimal _signalClose = 0;
    private bool _waitingForTrigger = false;
    private bool _longSignal = false;
    private bool _shortSignal = false;
    private Order _pendingOrder = null;
    private string _logFilePath;

    #endregion

    #region Public properties

    [Display(Name = "Volume", Order = 10)]
    [OFT.Attributes.Parameter]
    public decimal Volume { get; set; } = 1;

    [Display(Name = "Trigger Ticks", Order = 20)]
    [OFT.Attributes.Parameter]
    public int TriggerTicks { get; set; } = 3;

    [Display(Name = "Enable Logging", Order = 30)]
    [OFT.Attributes.Parameter]
    public bool EnableLogging { get; set; } = true;

    [Display(Name = "Close Position On Stopping", Order = 40)]
    [OFT.Attributes.Parameter]
    public bool ClosePositionOnStopping { get; set; } = true;

    #endregion

    #region Constructor

    public EchelonStrategy()
    {
        // Verstecke die Standard-DataSeries
        DataSeries[0].IsHidden = true;

        // Initialisiere Log-Datei
        if (EnableLogging)
        {
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            _logFilePath = Path.Combine(documentsPath, "ATAS", "Logs", $"EchelonStrategy_{DateTime.Now:yyyyMMdd_HHmmss}.log");

            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(_logFilePath));
                LogDebug("=== EchelonStrategy gestartet ===");
            }
            catch
            {
                // Ignore logging setup errors
            }
        }
    }

    #endregion

    #region Overrides

    protected override void OnCalculate(int bar, decimal value)
    {
        if (bar < 0)
            return;

        var candle = GetCandle(bar);
        if (candle == null)
            return;

        // Nur bei neuen Bars verarbeiten
        if (bar > _lastBar)
        {
            _lastBar = bar;

            // Prüfe ob wir auf einen Trigger warten
            if (_waitingForTrigger)
            {
                CheckForTrigger(bar, candle);
            }

            // Prüfe ob wir eine offene Order haben die gelöscht werden muss
            if (_pendingOrder != null && bar > _signalBar + 1)
            {
                // Trade-Kerze ist abgeschlossen ohne Ausführung -> Order löschen
                CancelPendingOrder();
            }
        }

        // Prüfe nur abgeschlossene Kerzen für Signale (mindestens 1 Bar zurück)
        if (bar >= CurrentBar - 1)
            return;

        // Prüfe Signal-Bedingungen für abgeschlossene Kerze
        CheckForSignal(bar, candle);
    }

    protected override void OnStopping()
    {
        if (CurrentPosition != 0 && ClosePositionOnStopping)
        {
            LogDebug($"Schließe Position {CurrentPosition} beim Stoppen");
            CloseCurrentPosition();
        }

        if (_pendingOrder != null)
        {
            CancelPendingOrder();
        }

        LogDebug("=== EchelonStrategy gestoppt ===");
        base.OnStopping();
    }

    #endregion

    #region Private methods

    private void CheckForSignal(int bar, IndicatorCandle candle)
    {
        // Prüfe ob bereits ein Signal aktiv ist
        if (_waitingForTrigger || _pendingOrder != null)
            return;

        LogDebug($"=== Prüfe Signal für Bar {bar} ===");
        LogDebug($"OHLC: O={candle.Open:F2}, H={candle.High:F2}, L={candle.Low:F2}, C={candle.Close:F2}");

        // Bestimme Kerzen-Typ
        bool isBearishCandle = candle.Close < candle.Open;
        bool isBullishCandle = candle.Close > candle.Open;

        if (!isBearishCandle && !isBullishCandle)
        {
            LogDebug("Doji-Kerze - kein Signal");
            return;
        }

        // Berechne Body-Grenzen
        decimal bodyTop = Math.Max(candle.Open, candle.Close);
        decimal bodyBottom = Math.Min(candle.Open, candle.Close);

        LogDebug($"Body: Top={bodyTop:F2}, Bottom={bodyBottom:F2}");

        // Verwende echte ATAS POC und Delta Berechnungen
        decimal pocPrice = GetPOCPrice(bar, candle);
        decimal pocDelta = GetPOCDelta(bar, candle);

        // Prüfe ob POC-Daten verfügbar sind
        if (pocPrice == 0)
        {
            LogDebug("POC-Daten nicht verfügbar - kein Signal");
            return;
        }

        LogDebug($"POC: Price={pocPrice:F2}, Delta={pocDelta:F2}");

        // Prüfe ob POC innerhalb des Bodies liegt
        bool pocInBody = pocPrice >= bodyBottom && pocPrice <= bodyTop;

        if (!pocInBody)
        {
            LogDebug("POC nicht im Body - kein Signal");
            return;
        }

        // Long Signal Bedingungen
        if (isBearishCandle && pocDelta < 0)
        {
            LogDebug("LONG SIGNAL erkannt!");
            _signalBar = bar;
            _signalClose = candle.Close;
            _longSignal = true;
            _shortSignal = false;
            _waitingForTrigger = true;
        }
        // Short Signal Bedingungen
        else if (isBullishCandle && pocDelta > 0)
        {
            LogDebug("SHORT SIGNAL erkannt!");
            _signalBar = bar;
            _signalClose = candle.Close;
            _longSignal = false;
            _shortSignal = true;
            _waitingForTrigger = true;
        }
        else
        {
            LogDebug("Signal-Bedingungen nicht erfüllt");
        }
    }

    private void CheckForTrigger(int bar, IndicatorCandle candle)
    {
        if (!_waitingForTrigger)
            return;

        var tickSize = InstrumentInfo?.TickSize ?? 0.01m;
        var triggerDistance = TriggerTicks * tickSize;

        LogDebug($"=== Prüfe Trigger für Bar {bar} ===");
        LogDebug($"Signal Close: {_signalClose:F2}, Current: H={candle.High:F2}, L={candle.Low:F2}");

        if (_longSignal)
        {
            // Prüfe ob Preis 3 Ticks unter Signal-Close gefallen ist
            if (candle.Low <= _signalClose - triggerDistance)
            {
                LogDebug($"LONG TRIGGER! Preis fiel auf {candle.Low:F2}, Trigger bei {_signalClose - triggerDistance:F2}");
                PlaceBuyStopOrder();
            }
        }
        else if (_shortSignal)
        {
            // Prüfe ob Preis 3 Ticks über Signal-Close gestiegen ist
            if (candle.High >= _signalClose + triggerDistance)
            {
                LogDebug($"SHORT TRIGGER! Preis stieg auf {candle.High:F2}, Trigger bei {_signalClose + triggerDistance:F2}");
                PlaceSellStopOrder();
            }
        }
    }

    private void PlaceBuyStopOrder()
    {
        try
        {
            // Verwende Market Order anstatt Stop Order für sofortige Ausführung
            LogDebug($"Platziere Buy Market Order bei Trigger-Ereignis");

            var order = new Order
            {
                Portfolio = Portfolio,
                Security = Security,
                Direction = OrderDirections.Buy,
                Type = OrderTypes.Market,
                QuantityToFill = Volume,
            };

            LogDebug($"Buy Market Order Details: Volume={Volume}");

            _pendingOrder = null; // Keine pending order bei Market Orders
            _waitingForTrigger = false;

            OpenOrder(order);
            ResetSignalState();
        }
        catch (Exception ex)
        {
            LogDebug($"Fehler beim Platzieren der Buy Market Order: {ex.Message}");
            ResetSignalState();
        }
    }

    private void PlaceSellStopOrder()
    {
        try
        {
            // Verwende Market Order anstatt Stop Order für sofortige Ausführung
            LogDebug($"Platziere Sell Market Order bei Trigger-Ereignis");

            var order = new Order
            {
                Portfolio = Portfolio,
                Security = Security,
                Direction = OrderDirections.Sell,
                Type = OrderTypes.Market,
                QuantityToFill = Volume,
            };

            LogDebug($"Sell Market Order Details: Volume={Volume}");

            _pendingOrder = null; // Keine pending order bei Market Orders
            _waitingForTrigger = false;

            OpenOrder(order);
            ResetSignalState();
        }
        catch (Exception ex)
        {
            LogDebug($"Fehler beim Platzieren der Sell Market Order: {ex.Message}");
            ResetSignalState();
        }
    }

    private void CancelPendingOrder()
    {
        if (_pendingOrder != null)
        {
            LogDebug("Lösche ausstehende Order");

            try
            {
                // TODO: Implementiere Order-Löschung mit ATAS API
                // CancelOrder(_pendingOrder);
            }
            catch (Exception ex)
            {
                LogDebug($"Fehler beim Löschen der Order: {ex.Message}");
            }

            ResetSignalState();
        }
    }

    private void ResetSignalState()
    {
        _signalBar = -1;
        _signalClose = 0;
        _waitingForTrigger = false;
        _longSignal = false;
        _shortSignal = false;
        _pendingOrder = null;

        LogDebug("Signal-Status zurückgesetzt");
    }

    private decimal GetPOCPrice(int bar, IndicatorCandle candle)
    {
        // Verwende die echte ATAS POC-Berechnung
        if (candle.MaxVolumePriceInfo == null)
        {
            LogDebug($"Keine MaxVolumePriceInfo (POC) für Bar {bar} verfügbar");
            return 0;
        }

        var pocPrice = candle.MaxVolumePriceInfo.Price;
        LogDebug($"POC-Preis: {pocPrice:F2}");

        return pocPrice;
    }

    private decimal GetPOCDelta(int bar, IndicatorCandle candle)
    {
        // Verwende die echte ATAS Delta-Berechnung
        if (candle.MaxVolumePriceInfo == null)
        {
            LogDebug($"Keine MaxVolumePriceInfo für Delta-Berechnung bei Bar {bar} verfügbar");
            return 0;
        }

        var pocBid = candle.MaxVolumePriceInfo.Bid;
        var pocAsk = candle.MaxVolumePriceInfo.Ask;
        var pocDelta = pocAsk - pocBid; // Delta = Ask Volume - Bid Volume

        LogDebug($"POC-Bid: {pocBid}, POC-Ask: {pocAsk}, POC-Delta: {pocDelta}");

        return pocDelta;
    }

    private void CloseCurrentPosition()
    {
        if (CurrentPosition == 0)
            return;

        try
        {
            var order = new Order
            {
                Portfolio = Portfolio,
                Security = Security,
                Direction = CurrentPosition > 0 ? OrderDirections.Sell : OrderDirections.Buy,
                Type = OrderTypes.Market,
                QuantityToFill = Math.Abs(CurrentPosition),
            };

            LogDebug($"Schließe Position: {CurrentPosition} -> Order: {order.Direction} {order.QuantityToFill}");
            OpenOrder(order);
        }
        catch (Exception ex)
        {
            LogDebug($"Fehler beim Schließen der Position: {ex.Message}");
        }
    }

    private void LogDebug(string message)
    {
        if (!EnableLogging || string.IsNullOrEmpty(_logFilePath))
            return;

        try
        {
            var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {message}";
            File.AppendAllText(_logFilePath, logMessage + Environment.NewLine);
        }
        catch
        {
            // Ignore logging errors
        }
    }

    #endregion
}