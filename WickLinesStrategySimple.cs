public class WickLinesStrategySimple : ATAS.Strategies.Chart.ChartStrategy
{
    #region Private fields

    private ATAS.Indicators.ValueDataSeries _upperWickLines;
    private ATAS.Indicators.ValueDataSeries _lowerWickLines;
    private int _lastBar;
    private int _minimumWickSizeTicks = 5;

    #endregion

    #region Public properties

    [System.ComponentModel.DataAnnotations.Display(
        Name = "Minimum Wick Size (Ticks)", 
        Order = 10)]
    [ATAS.Indicators.Parameter]
    public int MinimumWickSizeTicks
    {
        get => _minimumWickSizeTicks;
        set
        {
            _minimumWickSizeTicks = System.Math.Max(1, value);
            RecalculateValues();
        }
    }

    #endregion

    #region ctor

    public WickLinesStrategySimple()
    {
        // Verstecke die Standard-DataSeries
        DataSeries[0].IsHidden = true;

        // Erstelle DataSeries für obere Wick-Linien
        _upperWickLines = new ATAS.Indicators.ValueDataSeries("Upper Wick Lines")
        {
            VisualType = ATAS.Indicators.VisualMode.Line,
            Color = System.Drawing.Color.Red,
            Width = 2
        };
        DataSeries.Add(_upperWickLines);

        // Erstelle DataSeries für untere Wick-Linien
        _lowerWickLines = new ATAS.Indicators.ValueDataSeries("Lower Wick Lines")
        {
            VisualType = ATAS.Indicators.VisualMode.Line,
            Color = System.Drawing.Color.Blue,
            Width = 2
        };
        DataSeries.Add(_lowerWickLines);

        MinimumWickSizeTicks = 5;
    }

    #endregion

    #region Overrides of BaseIndicator

    protected override void OnCalculate(int bar, decimal value)
    {
        if (bar < 0 || bar >= CurrentBar)
            return;

        var candle = GetCandle(bar);
        if (candle == null)
            return;

        var tickSize = InstrumentInfo?.TickSize ?? 0.01m;
        var minimumWickSizePrice = MinimumWickSizeTicks * tickSize;

        // Berechne Wick-Größen
        var upperWickSize = CalculateUpperWickSize(candle);
        var lowerWickSize = CalculateLowerWickSize(candle);

        // Setze Werte für obere Wick-Linien
        if (upperWickSize >= minimumWickSizePrice)
        {
            _upperWickLines[bar] = candle.High;
        }
        else
        {
            _upperWickLines[bar] = 0;
        }

        // Setze Werte für untere Wick-Linien
        if (lowerWickSize >= minimumWickSizePrice)
        {
            _lowerWickLines[bar] = candle.Low;
        }
        else
        {
            _lowerWickLines[bar] = 0;
        }

        _lastBar = bar;
    }

    #endregion

    #region Private methods

    private decimal CalculateUpperWickSize(ATAS.Indicators.IndicatorCandle candle)
    {
        // Oberer Wick = High - Max(Open, Close)
        var bodyTop = System.Math.Max(candle.Open, candle.Close);
        return candle.High - bodyTop;
    }

    private decimal CalculateLowerWickSize(ATAS.Indicators.IndicatorCandle candle)
    {
        // Unterer Wick = Min(Open, Close) - Low
        var bodyBottom = System.Math.Min(candle.Open, candle.Close);
        return bodyBottom - candle.Low;
    }

    #endregion
}
